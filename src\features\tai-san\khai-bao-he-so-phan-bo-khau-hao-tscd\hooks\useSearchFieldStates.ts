import { useState, useEffect } from 'react';

export const useSearchFieldStates = (initialData?: any) => {
  const [taiSan, setTaiSan] = useState<any | null>(null);
  const [tkKhauHao, setTkKhauHao] = useState<any | null>(null);
  const [tkChiPhi, setTkChiPhi] = useState<any | null>(null);
  const [boPhanTS, setBoPhanTS] = useState<any | null>(null);
  const [boPhan, setBoPhan] = useState<any | null>(null);
  const [sanPham, setSanPham] = useState<any | null>(null);
  const [vuViec, setVuViec] = useState<any | null>(null);
  const [phi, setPhi] = useState<any | null>(null);
  const [hopDong, setHopDong] = useState<any | null>(null);

  // Update state when initialData changes
  useEffect(() => {
    if (initialData) {
      // Set taiSan from ma_ts_data if available
      if (initialData.ma_ts_data) {
        setTaiSan(initialData.ma_ts_data);
      }

      // Set tkKhauHao from tk_kh_data if available
      if (initialData.tk_kh_data) {
        setTkKhauHao(initialData.tk_kh_data);
      }

      // Set tkChiPhi from tk_cp_data if available
      if (initialData.tk_cp_data) {
        setTkChiPhi(initialData.tk_cp_data);
      }

      // Set boPhanTS from ma_bp_ts_data if available
      if (initialData.ma_bp_ts_data) {
        setBoPhanTS(initialData.ma_bp_ts_data);
      }

      // Set boPhan from ma_bp_data if available
      if (initialData.ma_bp_data) {
        setBoPhan(initialData.ma_bp_data);
      }

      // Set sanPham from ma_sp_data if available
      if (initialData.ma_sp_data) {
        setSanPham(initialData.ma_sp_data);
      }

      // Set vuViec from ma_vv_data if available
      if (initialData.ma_vv_data) {
        setVuViec(initialData.ma_vv_data);
      }

      // Set phi from ma_phi_data if available
      if (initialData.ma_phi_data) {
        setPhi(initialData.ma_phi_data);
      }

      // Set hopDong from ma_hd_data if available
      if (initialData.ma_hd_data) {
        setHopDong(initialData.ma_hd_data);
      }
    }
  }, [initialData]);

  return {
    taiSan,
    setTaiSan,
    boPhan,
    setBoPhan,
    boPhanTS,
    setBoPhanTS,
    sanPham,
    setSanPham,
    vuViec,
    setVuViec,
    tkKhauHao,
    setTkKhauHao,
    tkChiPhi,
    setTkChiPhi,
    phi,
    setPhi,
    hopDong,
    setHopDong
  };
};
